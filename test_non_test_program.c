#include <stdio.h>
#include <unistd.h>
#include "packages/libtestsocket/include/libtestsocket.h"

int main() {
    printf("Non-test program starting...\n");
    
    // 初始化测试socket，这会导致程序等待testd的run命令
    _test_init("non_test_program", NULL);
    
    printf("Non-test program continuing after TEST_INIT...\n");
    
    // 模拟一些工作
    for (int i = 0; i < 5; i++) {
        printf("Working... %d\n", i);
        sleep(1);
    }
    
    printf("Non-test program finished.\n");
    
    _test_cleanup();
    return 0;
}
