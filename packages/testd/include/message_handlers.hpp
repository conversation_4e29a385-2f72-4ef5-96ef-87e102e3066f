#pragma once

#include "message_system.hpp"
#include "context.hpp"
#include "json_protocol.hpp"
#include <memory>

namespace testd {

/**
 * @brief 初始化消息处理器
 * 处理来自客户端的init消息
 */
class InitMessageHandler : public MessageHandler {
public:
    InitMessageHandler() = default;
    virtual ~InitMessageHandler() = default;
    
    bool handleMessage(const EnhancedMessage& msg) override;
    std::string getName() const override { return "InitMessageHandler"; }
    int getPriority() const override { return 100; }  // 高优先级
};

/**
 * @brief 日志消息处理器
 * 处理来自客户端的log消息
 */
class LogMessageHandler : public MessageHandler {
public:
    LogMessageHandler() = default;
    virtual ~LogMessageHandler() = default;
    
    bool handleMessage(const EnhancedMessage& msg) override;
    std::string getName() const override { return "LogMessageHandler"; }
    int getPriority() const override { return 10; }
};

/**
 * @brief 测试验证消息处理器
 * 处理测试用例验证相关的消息
 */
class TestVerificationHandler : public MessageHandler {
public:
    TestVerificationHandler() = default;
    virtual ~TestVerificationHandler() = default;
    
    bool handleMessage(const EnhancedMessage& msg) override;
    std::string getName() const override { return "TestVerificationHandler"; }
    int getPriority() const override { return 50; }
};

/**
 * @brief 请求响应处理器
 * 处理带有ID的请求响应消息
 */
class RequestResponseHandler : public MessageHandler {
public:
    RequestResponseHandler() = default;
    virtual ~RequestResponseHandler() = default;
    
    bool handleMessage(const EnhancedMessage& msg) override;
    std::string getName() const override { return "RequestResponseHandler"; }
    int getPriority() const override { return 80; }
};

/**
 * @brief 错误消息处理器
 * 处理错误和异常消息
 */
class ErrorMessageHandler : public MessageHandler {
public:
    ErrorMessageHandler() = default;
    virtual ~ErrorMessageHandler() = default;
    
    bool handleMessage(const EnhancedMessage& msg) override;
    std::string getName() const override { return "ErrorMessageHandler"; }
    int getPriority() const override { return 90; }
};

/**
 * @brief 调试消息处理器
 * 处理调试相关的消息
 */
class DebugMessageHandler : public MessageHandler {
public:
    DebugMessageHandler() = default;
    virtual ~DebugMessageHandler() = default;
    
    bool handleMessage(const EnhancedMessage& msg) override;
    std::string getName() const override { return "DebugMessageHandler"; }
    int getPriority() const override { return 20; }
};

/**
 * @brief 默认消息处理器
 * 处理所有未被其他处理器处理的消息
 */
class DefaultMessageHandler : public MessageHandler {
public:
    DefaultMessageHandler() = default;
    virtual ~DefaultMessageHandler() = default;
    
    bool handleMessage(const EnhancedMessage& msg) override;
    std::string getName() const override { return "DefaultMessageHandler"; }
    int getPriority() const override { return -100; }  // 最低优先级
};

/**
 * @brief 消息处理器工厂
 * 用于创建和管理消息处理器
 */
class MessageHandlerFactory {
public:
    /**
     * @brief 创建所有默认的消息处理器
     */
    static std::vector<std::shared_ptr<MessageHandler>> createDefaultHandlers();
    
    /**
     * @brief 注册默认处理器到消息系统
     */
    static void registerDefaultHandlers();
    
    /**
     * @brief 创建特定类型的处理器
     */
    static std::shared_ptr<MessageHandler> createHandler(const std::string& type);
};

/**
 * @brief 消息处理器管理器
 * 管理消息处理器的生命周期和订阅
 */
class MessageHandlerManager {
public:
    MessageHandlerManager() = default;
    ~MessageHandlerManager();
    
    // 禁止拷贝和赋值
    MessageHandlerManager(const MessageHandlerManager&) = delete;
    MessageHandlerManager& operator=(const MessageHandlerManager&) = delete;
    
    /**
     * @brief 添加处理器
     */
    void addHandler(std::shared_ptr<MessageHandler> handler, const MessageFilter& filter);
    
    /**
     * @brief 移除处理器
     */
    void removeHandler(const std::string& handlerName);
    
    /**
     * @brief 获取处理器
     */
    std::shared_ptr<MessageHandler> getHandler(const std::string& handlerName);
    
    /**
     * @brief 清空所有处理器
     */
    void clear();
    
    /**
     * @brief 初始化默认处理器
     */
    void initializeDefaultHandlers();
    
private:
    std::vector<std::pair<std::shared_ptr<MessageHandler>, std::string>> handlers_;  // 处理器和订阅ID
};

} // namespace testd
