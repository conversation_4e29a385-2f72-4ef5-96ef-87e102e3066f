/**
 * @file json_protocol.hpp
 * @brief JSON协议处理模块头文件 - C++11版本
 */

#ifndef TESTD_JSON_PROTOCOL_HPP
#define TESTD_JSON_PROTOCOL_HPP

#include "testd.hpp"
#include "task.hpp"

namespace testd {

// 前向声明
struct EnhancedMessage;
class ClientConnection;


/**
 * @brief JSON协议处理类
 */
class JsonProtocol {
public:
    JsonProtocol() = default;
    ~JsonProtocol() = default;

    // 禁止拷贝和赋值
    JsonProtocol(const JsonProtocol&) = delete;
    JsonProtocol& operator=(const JsonProtocol&) = delete;

    // 创建测试模式设置请求
    static struct json_object* createSetModeRequest(TestMode mode);

    // 创建操作请求
    static struct json_object* createActionRequest(const std::string& action);

    // 创建切面请求
    static struct json_object* createAspectsRequest(const std::vector<std::string>& aspects);

    // 创建调用函数请求
    static struct json_object* createCallRequest(const std::string& func, struct json_object* args = nullptr);

    // 创建调试请求
    static struct json_object* createDebugRequest(const std::string& action, const std::string& addr,
                                               int bytes, const std::string& data = "");

    // 创建查询地址请求
    static struct json_object* createGetPtrRequest(const std::string& symbol);

    // 创建响应
    static struct json_object* createResponse(int result, const std::string& error = "",
                                           struct json_object* data = nullptr);

    // 创建通知
    static struct json_object* createNotify(const std::string& type, const std::string& data);

    // 解析请求
    static bool parseRequest(struct json_object* jsonObj, TestMode& mode, std::string& action);

    // 解析响应
    static bool parseResponse(struct json_object* jsonObj, int& result, std::string& error,
                            struct json_object** data);

    // 解析通知
    static bool parseNotify(struct json_object* jsonObj, std::string& type, std::string& data);

    // 创建测试控制请求
    static struct json_object* createTestControlRequest(const std::string& command,
                                                     const std::string& testId = "",
                                                     struct json_object* params = nullptr);

    // 创建测试控制响应
    static struct json_object* createTestControlResponse(const std::string& status,
                                                      const std::string& testId = "",
                                                      struct json_object* data = nullptr,
                                                      const std::string& error = "");

    // 解析测试控制请求
    static bool parseTestControlRequest(struct json_object* jsonObj, std::string& command,
                                      std::string& testId, struct json_object** params);

    // 解析测试控制响应
    static bool parseTestControlResponse(struct json_object* jsonObj, std::string& status,
                                       std::string& testId, struct json_object** data,
                                       std::string& error);

    // 处理消息
    static void processMessage(struct json_object* jsonObj, int clientIdx);

    // 解析消息
    static bool parseMessage(struct json_object* jsonObj, EnhancedMessage& msg, std::shared_ptr<ClientConnection> client);

    // 检查客户端是否被当前测试用例需要
    static bool isClientNeededByCurrentTest(std::shared_ptr<ClientConnection> client);

};

class RequestTask : public Task {
public:
    RequestTask(const std::string& name, const std::string& description, std::weak_ptr<ClientConnection> conn, struct json_object* request_obj, int timeout_ms, Private);
    static inline std::shared_ptr<RequestTask> create(const std::string& name, const std::string& description, std::weak_ptr<ClientConnection> conn, struct json_object* request_obj, int timeout_ms = -1)
    {
        return std::make_shared<RequestTask>(name, description, conn, request_obj, timeout_ms, Private());
    }
    virtual ~RequestTask();

    // 禁止拷贝和赋值
    RequestTask(const RequestTask&) = delete;
    RequestTask& operator=(const RequestTask&) = delete;

    virtual int executeCustom() override;

private:
    static int generateId();
    std::weak_ptr<ClientConnection> conn;
    struct json_object* request_obj;
    int end_time_s, end_time_ms;
    int id;

    // 消息系统相关
    std::string responseSubscriptionId_;
    bool responseReceived_;
    bool responseSuccess_;
};

} // namespace testd

#endif // TESTD_JSON_PROTOCOL_HPP
