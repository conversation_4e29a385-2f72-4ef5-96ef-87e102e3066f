/**
 * @file test_request_task.cpp
 * @brief 测试RequestTask使用新消息系统的功能
 */

#include "json_protocol.hpp"
#include "message_system.hpp"
#include "message_handlers.hpp"
#include "context.hpp"
#include <iostream>
#include <memory>
#include <thread>
#include <chrono>

using namespace testd;

/**
 * @brief 模拟客户端连接
 */
class MockClientConnection : public ClientConnection {
public:
    MockClientConnection() : ClientConnection(-1, 1234, "test_client") {}
    
    void simulateResponse(int requestId, int result, const std::string& error = "") {
        // 直接创建增强消息并发布
        EnhancedMessage msg;
        msg.type = "response";
        msg.id = "response_" + std::to_string(requestId);
        msg.idInt = requestId;
        msg.result = result;
        msg.error = error;
        msg.source = MessageSource::CLIENT;
        msg.sourceId = "test_client:1234";
        msg.priority = MessagePriority::NORMAL;
        msg.addTag("process:test_client");
        msg.addTag("pid:1234");

        MessageSystem::getInstance().publish(msg);

        std::cout << "Simulated response for request " << requestId
                  << " with result " << result << std::endl;
    }
};

int main() {
    std::cout << "=== RequestTask 新消息系统测试 ===" << std::endl;
    
    // 1. 初始化消息系统
    MessageHandlerFactory::registerDefaultHandlers();
    
    // 2. 创建模拟客户端连接
    auto mockClient = std::make_shared<MockClientConnection>();
    
    // 3. 创建测试请求
    struct json_object* request = json_object_new_object();
    json_object_object_add(request, "action", json_object_new_string("test"));
    json_object_object_add(request, "param", json_object_new_string("value"));
    
    std::cout << "\n--- 测试成功响应 ---" << std::endl;
    
    // 4. 创建RequestTask
    auto requestTask = RequestTask::create(
        "test_request", 
        "Test request with new message system",
        mockClient,
        request,
        5000  // 5秒超时
    );
    
    // 5. 执行任务初始化
    int result = requestTask->execute();
    std::cout << "Task init result: " << result << std::endl;
    
    // 6. 模拟处理消息
    MessageSystem::getInstance().processMessages();
    
    // 7. 模拟客户端响应（成功）
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    mockClient->simulateResponse(1, 0);  // 假设第一个请求ID是1
    
    // 8. 处理响应消息
    MessageSystem::getInstance().processMessages();
    
    // 9. 检查任务状态
    result = requestTask->execute();
    std::cout << "Task after success response: " << result << std::endl;
    std::cout << "Task state: " << static_cast<int>(requestTask->getState()) << std::endl;
    
    std::cout << "\n--- 测试错误响应 ---" << std::endl;
    
    // 10. 创建另一个请求测试错误情况
    struct json_object* request2 = json_object_new_object();
    json_object_object_add(request2, "action", json_object_new_string("test_error"));
    
    auto requestTask2 = RequestTask::create(
        "test_request_error", 
        "Test request error handling",
        mockClient,
        request2,
        5000
    );
    
    // 11. 执行任务初始化
    result = requestTask2->execute();
    std::cout << "Task2 init result: " << result << std::endl;
    
    // 12. 处理消息
    MessageSystem::getInstance().processMessages();
    
    // 13. 模拟客户端错误响应
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    mockClient->simulateResponse(2, -1, "Test error message");  // 假设第二个请求ID是2
    
    // 14. 处理响应消息
    MessageSystem::getInstance().processMessages();
    
    // 15. 检查任务状态
    result = requestTask2->execute();
    std::cout << "Task2 after error response: " << result << std::endl;
    std::cout << "Task2 state: " << static_cast<int>(requestTask2->getState()) << std::endl;
    std::cout << "Task2 error: " << requestTask2->getResultString() << std::endl;
    
    std::cout << "\n--- 测试超时情况 ---" << std::endl;
    
    // 16. 创建超时测试请求
    struct json_object* request3 = json_object_new_object();
    json_object_object_add(request3, "action", json_object_new_string("test_timeout"));
    
    auto requestTask3 = RequestTask::create(
        "test_request_timeout", 
        "Test request timeout",
        mockClient,
        request3,
        100  // 100ms超时
    );
    
    // 17. 执行任务初始化
    result = requestTask3->execute();
    std::cout << "Task3 init result: " << result << std::endl;
    
    // 18. 处理消息
    MessageSystem::getInstance().processMessages();
    
    // 19. 等待超时
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    // 20. 检查超时
    result = requestTask3->execute();
    std::cout << "Task3 after timeout: " << result << std::endl;
    std::cout << "Task3 state: " << static_cast<int>(requestTask3->getState()) << std::endl;
    std::cout << "Task3 error: " << requestTask3->getResultString() << std::endl;
    
    // 21. 清理
    std::cout << "\n--- 清理资源 ---" << std::endl;
    MessageSystem::getInstance().getRouter().clearSubscriptions();
    
    std::cout << "=== RequestTask 测试完成 ===" << std::endl;
    return 0;
}
