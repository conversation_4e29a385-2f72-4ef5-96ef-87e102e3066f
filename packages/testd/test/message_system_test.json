{"name": "消息系统测试", "description": "测试新的消息系统在测试验证中的功能", "setup": [{"command": "echo 'Setting up message system test'", "wait": 1000}], "cases": [{"name": "日志验证测试", "target": {"process": "test_logger", "aspect": "logging"}, "mock": [{"function": "log_message", "return": {"type": "string", "values": ["Test message logged successfully"]}}], "verify": {"expect_debug_log": {"type": "info", "message": "Test message logged successfully"}}}, {"name": "命令验证测试", "target": {"process": "test_command", "aspect": "execution"}, "verify": {"command": "echo 'Command executed successfully'", "expect_exit_code": 0}}, {"name": "混合验证测试", "target": {"process": "test_mixed", "aspect": "combined"}, "mock": [{"function": "process_data", "return": {"type": "int", "values": [42]}}], "verify": {"command": "test -f /tmp/test_output.txt", "expect_exit_code": 0, "expect_debug_log": {"type": "result", "message": "Processing completed with result: 42"}}}, {"name": "错误处理测试", "target": {"process": "test_error", "aspect": "error_handling"}, "mock": [{"function": "error_function", "return": {"type": "int", "values": [-1]}}], "verify": {"expect_debug_log": {"type": "error", "message": "Error occurred in processing"}}}], "cleanup": [{"command": "rm -f /tmp/test_output.txt"}, {"command": "echo 'Message system test cleanup completed'"}]}