/**
 * @file test_message_integration.cpp
 * @brief 测试消息系统与testd的集成
 */

#include "message_system.hpp"
#include "message_handlers.hpp"
#include "context.hpp"
#include "json_protocol.hpp"
#include <iostream>
#include <memory>
#include <thread>
#include <chrono>

using namespace testd;

/**
 * @brief 模拟客户端连接和消息发送
 */
class MockClient {
public:
    MockClient(const std::string& programName, pid_t pid) 
        : programName_(programName), pid_(pid) {
        // 创建模拟的客户端连接
        client_ = std::make_shared<ClientConnection>(-1, pid, programName);
    }
    
    void sendInitMessage() {
        // 直接创建增强消息
        EnhancedMessage msg;
        msg.type = "init";
        msg.id = "init_1";
        msg.idInt = 1;
        msg.source = MessageSource::CLIENT;
        msg.sourceId = programName_ + ":" + std::to_string(pid_);
        msg.priority = MessagePriority::HIGH;
        msg.addTag("process:" + programName_);
        msg.addTag("pid:" + std::to_string(pid_));
        msg.addTag("lifecycle");

        // 设置数据
        struct json_object* initData = json_object_new_object();
        json_object_object_add(initData, "pid", json_object_new_int(pid_));
        json_object_object_add(initData, "program", json_object_new_string(programName_.c_str()));
        msg.setData(initData);

        MessageSystem::getInstance().publish(msg);

        std::cout << "Sent init message for " << programName_ << std::endl;
    }
    
    void sendLogMessage(const std::string& logType, const std::string& message) {
        // 直接创建增强消息
        EnhancedMessage msg;
        msg.type = "log";
        msg.subType = logType;
        msg.id = "log_" + std::to_string(++messageCounter_);
        msg.idInt = messageCounter_;
        msg.source = MessageSource::CLIENT;
        msg.sourceId = programName_ + ":" + std::to_string(pid_);
        msg.priority = MessagePriority::LOW;
        msg.addTag("process:" + programName_);
        msg.addTag("pid:" + std::to_string(pid_));
        msg.addTag("logging");

        // 设置数据
        struct json_object* logData = json_object_new_string(message.c_str());
        msg.setData(logData);

        MessageSystem::getInstance().publish(msg);

        std::cout << "Sent log message: [" << logType << "] " << message << std::endl;
    }
    
    void sendErrorMessage(const std::string& error) {
        // 直接创建增强消息
        EnhancedMessage msg;
        msg.type = "error";
        msg.error = error;
        msg.id = "error_" + std::to_string(++messageCounter_);
        msg.idInt = messageCounter_;
        msg.result = -1;
        msg.source = MessageSource::CLIENT;
        msg.sourceId = programName_ + ":" + std::to_string(pid_);
        msg.priority = MessagePriority::CRITICAL;
        msg.addTag("process:" + programName_);
        msg.addTag("pid:" + std::to_string(pid_));
        msg.addTag("error");

        MessageSystem::getInstance().publish(msg);

        std::cout << "Sent error message: " << error << std::endl;
    }
    
private:
    std::string programName_;
    pid_t pid_;
    std::shared_ptr<ClientConnection> client_;
    static int messageCounter_;
};

int MockClient::messageCounter_ = 0;

/**
 * @brief 自定义测试处理器
 */
class TestMessageHandler : public MessageHandler {
public:
    TestMessageHandler() : messageCount_(0) {}
    
    bool handleMessage(const EnhancedMessage& msg) override {
        messageCount_++;
        std::cout << "[TEST HANDLER] Received message #" << messageCount_ 
                  << " - Type: " << msg.type 
                  << ", Source: " << msg.sourceId 
                  << ", Priority: " << static_cast<int>(msg.priority) << std::endl;
        
        if (msg.hasTag("process:test_app")) {
            std::cout << "  -> This is from test_app process" << std::endl;
        }
        
        return false;  // 不消费消息
    }
    
    std::string getName() const override { return "TestMessageHandler"; }
    int getPriority() const override { return 30; }
    
    int getMessageCount() const { return messageCount_; }
    
private:
    int messageCount_;
};

int main() {
    std::cout << "=== testd 消息系统集成测试 ===" << std::endl;
    
    // 1. 初始化消息系统
    MessageHandlerFactory::registerDefaultHandlers();
    
    // 2. 添加自定义测试处理器
    auto testHandler = std::make_shared<TestMessageHandler>();
    MessageFilter testFilter;
    testFilter.byTag("process:test_app");
    MessageSystem::getInstance().subscribe(testFilter, testHandler, 30, false);
    
    // 3. 创建模拟客户端
    MockClient client1("test_app", 1234);
    MockClient client2("other_app", 5678);
    
    std::cout << "\n--- 发送测试消息 ---" << std::endl;
    
    // 4. 发送各种类型的消息
    client1.sendInitMessage();
    client1.sendLogMessage("info", "Application started successfully");
    client1.sendLogMessage("debug", "Processing user request");
    client1.sendLogMessage("error", "Failed to connect to database");
    client1.sendErrorMessage("Critical system error");
    
    client2.sendInitMessage();
    client2.sendLogMessage("info", "Other application running");
    
    std::cout << "\n--- 处理消息 ---" << std::endl;
    
    // 5. 处理消息
    MessageSystem::getInstance().processMessages();
    
    std::cout << "\n--- 测试结果 ---" << std::endl;
    std::cout << "测试处理器处理的消息数量: " << testHandler->getMessageCount() << std::endl;
    
    // 6. 测试动态订阅
    std::cout << "\n--- 测试动态订阅 ---" << std::endl;
    
    // 订阅错误消息
    MessageFilter errorFilter;
    errorFilter.byPriority(MessagePriority::CRITICAL);
    
    class ErrorCountHandler : public MessageHandler {
    public:
        ErrorCountHandler() : errorCount_(0) {}
        bool handleMessage(const EnhancedMessage& msg) override {
            errorCount_++;
            std::cout << "[ERROR COUNTER] Critical message #" << errorCount_
                      << " from " << msg.sourceId << std::endl;
            return false;
        }
        std::string getName() const override { return "ErrorCountHandler"; }
        int getPriority() const override { return 95; }
        int getErrorCount() const { return errorCount_; }
    private:
        int errorCount_;  // 改为非静态成员
    };
    
    auto errorHandler = std::make_shared<ErrorCountHandler>();
    std::string errorSub = MessageSystem::getInstance().subscribe(errorFilter, errorHandler, 95, false);
    
    // 发送更多错误消息
    client1.sendErrorMessage("Another critical error");
    client2.sendErrorMessage("System failure");
    
    // 处理消息
    MessageSystem::getInstance().processMessages();
    
    std::cout << "错误处理器处理的消息数量: " << errorHandler->getErrorCount() << std::endl;
    
    // 7. 清理
    std::cout << "\n--- 清理资源 ---" << std::endl;
    MessageSystem::getInstance().getRouter().unsubscribe(errorSub);
    MessageSystem::getInstance().getRouter().clearSubscriptions();
    
    std::cout << "=== 集成测试完成 ===" << std::endl;
    return 0;
}

// 静态成员定义需要在类外部
// 这里应该是空的，因为ErrorCountHandler是在main函数内部定义的局部类
