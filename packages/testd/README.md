# testd - 跨语言切面测试平台控制端 (C++11版本)

testd是跨语言切面测试平台的控制端组件，负责与被控端（C程序、Shell脚本、Lua脚本）进行通信，并提供测试用例管理、结果分析等功能。

## 功能特性

- **通信管理**：
  - 管理与被控端的Unix Domain Socket连接
  - 处理TCP Socket远程控制连接
  - 使用JSON协议进行通信

- **测试用例管理**：
  - 测试用例加载和解析
  - 测试序列执行
  - 依赖关系处理

- **结果分析**：
  - 收集测试结果
  - 分析成功/失败状态
  - 生成测试报告

- **配置管理**：
  - 读取配置文件
  - 命令行参数处理
  - 运行时配置调整

- **消息系统** (已完全重构)：
  - 基于订阅模式的消息处理架构（完全替代旧的硬编码逻辑）
  - 统一的EnhancedMessage消息类型（已移除ClientMessageType）
  - 支持多维度消息过滤（类型、来源、优先级、标签）
  - 插件化的消息处理器
  - 智能消息路由和分发
  - 动态订阅管理
  - 线程安全的消息处理
  - 已完全移除旧的消息队列和过滤机制

## C++11特性

本版本使用C++11重构，主要使用了以下C++11特性：

- 智能指针（std::shared_ptr, std::unique_ptr）
- 右值引用和移动语义
- Lambda表达式
- 类型推导（auto）
- 范围for循环
- 线程库（std::thread, std::mutex）
- 原子操作（std::atomic）
- nullptr关键字
- 强类型枚举（enum class）
- 初始化列表
- 委托构造函数
- 显式虚函数覆盖（override）
- 显式禁用函数（= delete）

## 命令行接口

```
testd [选项] [命令]

命令:
  run <测试用例文件>     运行指定测试用例
  server                启动控制服务器
  mock <进程ID> <函数>  直接Mock指定进程的函数
  call <进程ID> <函数>  直接调用指定进程的函数

选项:
  -p, --port <端口>     指定TCP控制端口（默认8000）
  -v, --verbose         详细输出模式
  -c, --config <文件>   指定配置文件
  -o, --output <文件>   指定输出文件
  -h, --help            显示帮助信息
  -V, --version         显示版本信息
```

## 代码结构

- **include/**: 头文件目录
  - `testd.hpp`: 主头文件，包含基本定义和前置声明
  - `context.hpp`: 全局上下文类定义
  - `socket.hpp`: Socket通信类定义
  - `test_case.hpp`: 测试用例类定义
  - `json_protocol.hpp`: JSON协议处理类定义

- **src/**: 源文件目录
  - `main.cpp`: 主程序入口
  - `context.cpp`: 全局上下文类实现
  - `socket.cpp`: Socket通信类实现
  - `test_case.cpp`: 测试用例类实现
  - `json_protocol.cpp`: JSON协议处理类实现

- **test/**: 测试文件目录
  - `run_tests.sh`: 测试脚本
  - `example_test.json`: 示例测试用例

## 编译与安装

```bash
make
make install
```

## 使用示例

### 启动控制服务器
```bash
testd server -p 8080
```

### 运行测试用例
```bash
testd run test/example_test.json
```

### 直接Mock函数
```bash
testd mock 1234 network_connect
```

### 直接调用函数
```bash
testd call 1234 network_send '{"data": "test"}'
```

## 消息系统使用

### 基本概念

新的消息系统采用发布-订阅模式，支持灵活的消息过滤和处理：

```cpp
// 订阅特定类型的消息
MessageFilter filter;
filter.byType("log").bySubType("error").byTag("process:nginx");

auto handler = std::make_shared<CustomHandler>();
MessageSystem::getInstance().subscribe(filter, handler, 50, false);
```

### 消息过滤

支持多维度过滤：
- **按类型**: `filter.byType("log")`
- **按子类型**: `filter.bySubType("error")`
- **按来源**: `filter.bySource(MessageSource::CLIENT)`
- **按优先级**: `filter.byPriority(MessagePriority::HIGH)`
- **按标签**: `filter.byTag("process:nginx")`
- **自定义条件**: `filter.byCustom([](const EnhancedMessage& msg) { ... })`

### 自定义处理器

```cpp
class MyHandler : public MessageHandler {
public:
    bool handleMessage(const EnhancedMessage& msg) override {
        // 处理消息逻辑
        return true;  // true=消费消息，false=继续传递
    }
    std::string getName() const override { return "MyHandler"; }
    int getPriority() const override { return 50; }
};
```

### 示例和文档

- 📖 详细文档: `docs/message_system.md`
- 💡 使用示例: `examples/message_system_example.cpp`
- 🧪 集成测试: `test/test_message_integration.cpp`

## 依赖

- json-c: JSON解析库
- uuid: UUID生成库