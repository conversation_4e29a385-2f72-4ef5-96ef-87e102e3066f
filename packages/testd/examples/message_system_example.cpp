/**
 * @file message_system_example.cpp
 * @brief 消息系统使用示例
 */

#include "message_system.hpp"
#include "message_handlers.hpp"
#include <iostream>
#include <memory>

using namespace testd;

/**
 * @brief 自定义消息处理器示例
 */
class CustomLogHandler : public MessageHandler {
public:
    bool handleMessage(const EnhancedMessage& msg) override {
        if (msg.type == "log" && msg.subType == "custom") {
            std::cout << "[CUSTOM LOG] " << msg.sourceId << ": ";
            if (msg.getData()) {
                const char* jsonStr = json_object_to_json_string(msg.getData());
                if (jsonStr) {
                    std::cout << jsonStr;
                }
            }
            std::cout << std::endl;
            return true;  // 消费消息
        }
        return false;
    }
    
    std::string getName() const override {
        return "CustomLogHandler";
    }
    
    int getPriority() const override {
        return 50;
    }
};

/**
 * @brief 特定进程消息处理器示例
 */
class ProcessSpecificHandler : public MessageHandler {
public:
    ProcessSpecificHandler(const std::string& processName) : processName_(processName) {}
    
    bool handleMessage(const EnhancedMessage& msg) override {
        if (msg.hasTag("process:" + processName_)) {
            std::cout << "[PROCESS:" << processName_ << "] Received " << msg.type 
                      << " message with priority " << static_cast<int>(msg.priority) << std::endl;
            return false;  // 不消费消息，让其他处理器也能处理
        }
        return false;
    }
    
    std::string getName() const override {
        return "ProcessSpecificHandler_" + processName_;
    }
    
    int getPriority() const override {
        return 60;
    }
    
private:
    std::string processName_;
};

int main() {
    std::cout << "=== 消息系统使用示例 ===" << std::endl;
    
    // 1. 获取消息系统实例
    auto& messageSystem = MessageSystem::getInstance();
    
    // 2. 注册默认处理器
    MessageHandlerFactory::registerDefaultHandlers();
    
    // 3. 创建自定义处理器
    auto customLogHandler = std::make_shared<CustomLogHandler>();
    auto processHandler = std::make_shared<ProcessSpecificHandler>("test_program");
    
    // 4. 订阅特定类型的消息
    
    // 订阅自定义日志消息
    MessageFilter customLogFilter;
    customLogFilter.byType("log").bySubType("custom");
    std::string customLogSub = messageSystem.subscribe(customLogFilter, customLogHandler, 50, true);
    
    // 订阅特定进程的所有消息
    MessageFilter processFilter;
    processFilter.byTag("process:test_program");
    std::string processSub = messageSystem.subscribe(processFilter, processHandler, 60, false);
    
    // 订阅高优先级消息
    MessageFilter highPriorityFilter;
    highPriorityFilter.byPriority(MessagePriority::HIGH);

    // 创建高优先级处理器
    class HighPriorityHandler : public MessageHandler {
    public:
        bool handleMessage(const EnhancedMessage& msg) override {
            std::cout << "[HIGH PRIORITY] " << msg.type << " from " << msg.sourceId << std::endl;
            return false;
        }
        std::string getName() const override { return "HighPriorityHandler"; }
        int getPriority() const override { return 80; }
    };

    auto highPriorityHandler = std::make_shared<HighPriorityHandler>();
    std::string highPrioritySub = messageSystem.subscribe(highPriorityFilter, highPriorityHandler, 80, false);
    
    // 5. 创建和发布测试消息
    
    // 创建普通日志消息
    EnhancedMessage logMsg;
    logMsg.type = "log";
    logMsg.subType = "info";
    logMsg.source = MessageSource::CLIENT;
    logMsg.sourceId = "test_program:1234";
    logMsg.priority = MessagePriority::NORMAL;
    logMsg.addTag("process:test_program");
    logMsg.addTag("logging");
    
    // 创建自定义日志消息
    EnhancedMessage customLogMsg;
    customLogMsg.type = "log";
    customLogMsg.subType = "custom";
    customLogMsg.source = MessageSource::CLIENT;
    customLogMsg.sourceId = "test_program:1234";
    customLogMsg.priority = MessagePriority::NORMAL;
    customLogMsg.addTag("process:test_program");
    customLogMsg.addTag("custom");
    
    // 创建高优先级消息
    EnhancedMessage highPriorityMsg;
    highPriorityMsg.type = "init";
    highPriorityMsg.source = MessageSource::CLIENT;
    highPriorityMsg.sourceId = "test_program:1234";
    highPriorityMsg.priority = MessagePriority::HIGH;
    highPriorityMsg.addTag("process:test_program");
    highPriorityMsg.addTag("lifecycle");
    
    // 创建错误消息
    EnhancedMessage errorMsg;
    errorMsg.type = "error";
    errorMsg.source = MessageSource::CLIENT;
    errorMsg.sourceId = "test_program:1234";
    errorMsg.priority = MessagePriority::CRITICAL;
    errorMsg.error = "Test error message";
    errorMsg.addTag("process:test_program");
    errorMsg.addTag("error");
    
    // 6. 发布消息
    std::cout << "\n--- 发布消息 ---" << std::endl;
    
    std::cout << "发布普通日志消息..." << std::endl;
    messageSystem.publish(logMsg);
    
    std::cout << "发布自定义日志消息..." << std::endl;
    messageSystem.publish(customLogMsg);
    
    std::cout << "发布高优先级消息..." << std::endl;
    messageSystem.publish(highPriorityMsg);
    
    std::cout << "发布错误消息..." << std::endl;
    messageSystem.publish(errorMsg);
    
    // 7. 处理消息
    std::cout << "\n--- 处理消息 ---" << std::endl;
    messageSystem.processMessages();
    
    // 8. 演示动态订阅和取消订阅
    std::cout << "\n--- 动态订阅管理 ---" << std::endl;
    
    // 取消自定义日志订阅
    messageSystem.getRouter().unsubscribe(customLogSub);
    std::cout << "已取消自定义日志订阅" << std::endl;
    
    // 再次发布自定义日志消息
    std::cout << "再次发布自定义日志消息..." << std::endl;
    messageSystem.publish(customLogMsg);
    messageSystem.processMessages();
    
    // 9. 清理
    std::cout << "\n--- 清理资源 ---" << std::endl;
    messageSystem.getRouter().clearSubscriptions();
    
    std::cout << "=== 示例结束 ===" << std::endl;
    return 0;
}
