#include "message_handlers.hpp"
#include "socket.hpp"
#include <iostream>
#include <algorithm>

namespace testd {

// InitMessageHandler 实现
bool InitMessageHandler::handleMessage(const EnhancedMessage& msg) {
    if (msg.type != "init") {
        return false;
    }
    
    // 获取客户端
    auto& clients = Context::getInstance().getClients();
    std::shared_ptr<ClientConnection> client = nullptr;
    
    // 根据sourceId查找客户端
    for (const auto& c : clients) {
        std::string clientId = c->getProgramName() + ":" + std::to_string(c->getPid());
        if (clientId == msg.sourceId) {
            client = c;
            break;
        }
    }
    
    if (!client) {
        std::cerr << "Client not found for init message: " << msg.sourceId << std::endl;
        return false;
    }
    
    // 处理初始化消息
    struct json_object* dataObj = msg.getData();
    if (dataObj) {
        struct json_object* pidObj;
        if (json_object_object_get_ex(dataObj, "pid", &pidObj)) {
            int pid = json_object_get_int(pidObj);
            client->setPid(pid);
        }

        struct json_object* programObj;
        if (json_object_object_get_ex(dataObj, "program", &programObj)) {
            const char* program = json_object_get_string(programObj);
            if (program) {
                client->setProgramName(program);
            }
        }
    }

    // 检查是否为测试程序，如果不是则自动发送run命令
    bool isTestProgram = false;
    auto currentTest = Context::getInstance().getCurrentTest();
    if (currentTest) {
        // 检查当前测试用例是否需要这个程序
        isTestProgram = JsonProtocol::isClientNeededByCurrentTest(client);
    }

    // 如果不是测试程序，自动发送run命令让程序继续执行
    if (!isTestProgram) {
        struct json_object* runRequest = JsonProtocol::createActionRequest("run");
        if (runRequest) {
            Socket::sendJson(client, runRequest);
            json_object_put(runRequest);
            if (Context::getInstance().getConfig().isVerbose()) {
                std::cout << "Auto-sent run command to non-test program: "
                          << client->getProgramName() << " (PID: " << client->getPid() << ")" << std::endl;
            }
        }
    }
    
    return true;  // 消息已处理
}

// LogMessageHandler 实现
bool LogMessageHandler::handleMessage(const EnhancedMessage& msg) {
    if (msg.type != "log") {
        return false;
    }
    
    // 处理日志消息
    if (Context::getInstance().getConfig().isVerbose()) {
        std::cout << "[LOG:" << msg.subType << "] from " << msg.sourceId 
                  << " at " << msg.timestamp << std::endl;
        
        if (msg.getData()) {
            const char* jsonStr = json_object_to_json_string(msg.getData());
            if (jsonStr) {
                std::cout << "  Data: " << jsonStr << std::endl;
            }
        }
    }
    
    return false;  // 不消费消息，让其他处理器也能处理
}

// TestVerificationHandler 实现
bool TestVerificationHandler::handleMessage(const EnhancedMessage& msg) {
    if (msg.type != "log" || !msg.hasTag("logging")) {
        return false;
    }
    
    // 这里可以实现测试验证逻辑
    // 例如检查特定的日志类型是否符合测试期望
    auto currentTest = Context::getInstance().getCurrentTest();
    if (currentTest) {
        // 可以在这里添加测试验证逻辑
        // 例如检查日志内容是否匹配期望的模式
    }
    
    return false;  // 不消费消息
}

// RequestResponseHandler 实现
bool RequestResponseHandler::handleMessage(const EnhancedMessage& msg) {
    if (msg.idInt == 0 && msg.id.empty()) {
        return false;  // 不是请求响应消息
    }
    
    // 处理带有ID的响应消息
    // 这里可以实现请求-响应匹配逻辑
    if (Context::getInstance().getConfig().isVerbose()) {
        std::cout << "[RESPONSE] ID: " << msg.id << " (" << msg.idInt << ") "
                  << "Result: " << msg.result;
        if (!msg.error.empty()) {
            std::cout << " Error: " << msg.error;
        }
        std::cout << std::endl;
    }
    
    return false;  // 不消费消息，让其他处理器处理
}

// ErrorMessageHandler 实现
bool ErrorMessageHandler::handleMessage(const EnhancedMessage& msg) {
    if (msg.priority != MessagePriority::CRITICAL && !msg.hasTag("error")) {
        return false;
    }
    
    // 处理错误消息
    std::cerr << "[ERROR] from " << msg.sourceId << ": " << msg.error << std::endl;
    if (msg.getData()) {
        const char* jsonStr = json_object_to_json_string(msg.getData());
        if (jsonStr) {
            std::cerr << "  Error data: " << jsonStr << std::endl;
        }
    }
    
    return false;  // 不消费消息
}

// DebugMessageHandler 实现
bool DebugMessageHandler::handleMessage(const EnhancedMessage& msg) {
    if (!msg.hasTag("debug") && msg.type != "debug") {
        return false;
    }
    
    // 处理调试消息
    if (Context::getInstance().getConfig().isVerbose()) {
        std::cout << "[DEBUG] " << msg.type << " from " << msg.sourceId << std::endl;
        if (msg.getData()) {
            const char* jsonStr = json_object_to_json_string(msg.getData());
            if (jsonStr) {
                std::cout << "  Debug data: " << jsonStr << std::endl;
            }
        }
    }
    
    return false;  // 不消费消息
}

// DefaultMessageHandler 实现
bool DefaultMessageHandler::handleMessage(const EnhancedMessage& msg) {
    // 处理所有未被其他处理器处理的消息
    if (Context::getInstance().getConfig().isVerbose()) {
        std::cout << "[DEFAULT] Unhandled message type: " << msg.type 
                  << " from " << msg.sourceId << std::endl;
    }
    
    return false;  // 不消费消息
}

// MessageHandlerFactory 实现
std::vector<std::shared_ptr<MessageHandler>> MessageHandlerFactory::createDefaultHandlers() {
    std::vector<std::shared_ptr<MessageHandler>> handlers;
    
    handlers.push_back(std::make_shared<InitMessageHandler>());
    handlers.push_back(std::make_shared<LogMessageHandler>());
    handlers.push_back(std::make_shared<TestVerificationHandler>());
    handlers.push_back(std::make_shared<RequestResponseHandler>());
    handlers.push_back(std::make_shared<ErrorMessageHandler>());
    handlers.push_back(std::make_shared<DebugMessageHandler>());
    handlers.push_back(std::make_shared<DefaultMessageHandler>());
    
    return handlers;
}

void MessageHandlerFactory::registerDefaultHandlers() {
    auto& messageSystem = MessageSystem::getInstance();
    
    // 注册初始化消息处理器
    MessageFilter initFilter;
    initFilter.byType("init");
    messageSystem.subscribe(initFilter, std::make_shared<InitMessageHandler>(), 100, true);
    
    // 注册日志消息处理器
    MessageFilter logFilter;
    logFilter.byType("log");
    messageSystem.subscribe(logFilter, std::make_shared<LogMessageHandler>(), 10, false);
    
    // 注册错误消息处理器
    MessageFilter errorFilter;
    errorFilter.byPriority(MessagePriority::CRITICAL);
    messageSystem.subscribe(errorFilter, std::make_shared<ErrorMessageHandler>(), 90, false);
    
    // 注册请求响应处理器
    MessageFilter responseFilter;
    responseFilter.byCustom([](const EnhancedMessage& msg) {
        return msg.idInt != 0 || !msg.id.empty();
    });
    messageSystem.subscribe(responseFilter, std::make_shared<RequestResponseHandler>(), 80, false);
    
    // 注册调试消息处理器
    MessageFilter debugFilter;
    debugFilter.byTag("debug");
    messageSystem.subscribe(debugFilter, std::make_shared<DebugMessageHandler>(), 20, false);
    
    // 注册默认处理器（处理所有消息）
    MessageFilter defaultFilter;  // 空过滤器匹配所有消息
    messageSystem.subscribe(defaultFilter, std::make_shared<DefaultMessageHandler>(), -100, false);
}

std::shared_ptr<MessageHandler> MessageHandlerFactory::createHandler(const std::string& type) {
    if (type == "init") {
        return std::make_shared<InitMessageHandler>();
    } else if (type == "log") {
        return std::make_shared<LogMessageHandler>();
    } else if (type == "test") {
        return std::make_shared<TestVerificationHandler>();
    } else if (type == "response") {
        return std::make_shared<RequestResponseHandler>();
    } else if (type == "error") {
        return std::make_shared<ErrorMessageHandler>();
    } else if (type == "debug") {
        return std::make_shared<DebugMessageHandler>();
    } else {
        return std::make_shared<DefaultMessageHandler>();
    }
}

// MessageHandlerManager 实现
MessageHandlerManager::~MessageHandlerManager() {
    clear();
}

void MessageHandlerManager::addHandler(std::shared_ptr<MessageHandler> handler, const MessageFilter& filter) {
    if (!handler) {
        return;
    }

    // 检查是否已经存在同名处理器
    auto it = std::find_if(handlers_.begin(), handlers_.end(),
                          [&handler](const std::pair<std::shared_ptr<MessageHandler>, std::string>& pair) {
                              return pair.first->getName() == handler->getName();
                          });

    if (it != handlers_.end()) {
        // 移除旧的处理器
        MessageSystem::getInstance().getRouter().unsubscribe(it->second);
        handlers_.erase(it);
    }

    // 添加新的处理器
    std::string subscriptionId = MessageSystem::getInstance().subscribe(
        filter, handler, handler->getPriority(), false);

    handlers_.emplace_back(handler, subscriptionId);
}

void MessageHandlerManager::removeHandler(const std::string& handlerName) {
    auto it = std::find_if(handlers_.begin(), handlers_.end(),
                          [&handlerName](const std::pair<std::shared_ptr<MessageHandler>, std::string>& pair) {
                              return pair.first->getName() == handlerName;
                          });

    if (it != handlers_.end()) {
        MessageSystem::getInstance().getRouter().unsubscribe(it->second);
        handlers_.erase(it);
    }
}

std::shared_ptr<MessageHandler> MessageHandlerManager::getHandler(const std::string& handlerName) {
    auto it = std::find_if(handlers_.begin(), handlers_.end(),
                          [&handlerName](const std::pair<std::shared_ptr<MessageHandler>, std::string>& pair) {
                              return pair.first->getName() == handlerName;
                          });

    if (it != handlers_.end()) {
        return it->first;
    }

    return nullptr;
}

void MessageHandlerManager::clear() {
    for (const auto& pair : handlers_) {
        MessageSystem::getInstance().getRouter().unsubscribe(pair.second);
    }
    handlers_.clear();
}

void MessageHandlerManager::initializeDefaultHandlers() {
    // 清空现有处理器
    clear();

    // 添加初始化消息处理器
    MessageFilter initFilter;
    initFilter.byType("init");
    addHandler(std::make_shared<InitMessageHandler>(), initFilter);

    // 添加日志消息处理器
    MessageFilter logFilter;
    logFilter.byType("log");
    addHandler(std::make_shared<LogMessageHandler>(), logFilter);

    // 添加测试验证处理器
    MessageFilter testFilter;
    testFilter.byTag("logging");
    addHandler(std::make_shared<TestVerificationHandler>(), testFilter);

    // 添加错误消息处理器
    MessageFilter errorFilter;
    errorFilter.byPriority(MessagePriority::CRITICAL);
    addHandler(std::make_shared<ErrorMessageHandler>(), errorFilter);

    // 添加请求响应处理器
    MessageFilter responseFilter;
    responseFilter.byCustom([](const EnhancedMessage& msg) {
        return msg.idInt != 0 || !msg.id.empty();
    });
    addHandler(std::make_shared<RequestResponseHandler>(), responseFilter);

    // 添加调试消息处理器
    MessageFilter debugFilter;
    debugFilter.byTag("debug");
    addHandler(std::make_shared<DebugMessageHandler>(), debugFilter);

    // 添加默认处理器
    MessageFilter defaultFilter;  // 空过滤器匹配所有消息
    addHandler(std::make_shared<DefaultMessageHandler>(), defaultFilter);
}

} // namespace testd
