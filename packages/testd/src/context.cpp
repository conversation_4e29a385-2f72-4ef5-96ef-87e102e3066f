/**
 * @file context.cpp
 * @brief 全局上下文类实现 - C++11版本
 */

#include "context.hpp"
#include "socket.hpp"
#include "test_case.hpp"
#include "json_protocol.hpp"
#include "message_handlers.hpp"
#include <iostream>
#include <getopt.h>
#include <cstring>
#include <memory>
#include <sys/select.h>
#include <sys/time.h>

namespace testd {

// Config类实现
Config::Config() : tcpPort(8000), verbose(false), configFile(""), outputFile("") {
}

bool Config::parseArgs(int argc, char** argv, CommandType& cmdType, std::string& testFile, 
                      pid_t& targetPid, std::string& functionName, struct json_object*& params) {
    static struct option long_options[] = {
        {"port", required_argument, 0, 'p'},
        {"verbose", no_argument, 0, 'v'},
        {"config", required_argument, 0, 'c'},
        {"output", required_argument, 0, 'o'},
        {"help", no_argument, 0, 'h'},
        {"version", no_argument, 0, 'V'},
        {0, 0, 0, 0}
    };

    int opt;
    int option_index = 0;
    cmdType = CommandType::NONE;

    while ((opt = getopt_long(argc, argv, "p:vc:o:hV", long_options, &option_index)) != -1) {
        switch (opt) {
            case 'p':
                tcpPort = std::stoi(optarg);
                break;
            case 'v':
                verbose = true;
                break;
            case 'c':
                configFile = optarg;
                break;
            case 'o':
                outputFile = optarg;
                break;
            case 'h':
                printHelp();
                return false;
            case 'V':
                printVersion();
                return false;
            case '?':
                return false;
            default:
                break;
        }
    }

    // 处理命令
    if (optind < argc) {
        std::string command = argv[optind];
        
        if (command == "server") {
            cmdType = CommandType::SERVER;
        } else if (command == "run" && optind + 1 < argc) {
            cmdType = CommandType::RUN_TEST;
            testFile = argv[optind + 1];
        } else if (command == "mock" && optind + 2 < argc) {
            cmdType = CommandType::MOCK_FUNCTION;
            targetPid = std::stoi(argv[optind + 1]);
            functionName = argv[optind + 2];
            
            // 如果有参数，解析JSON
            if (optind + 3 < argc) {
                params = json_tokener_parse(argv[optind + 3]);
                if (!params) {
                    std::cerr << "Invalid JSON parameters" << std::endl;
                    return false;
                }
            }
        } else if (command == "call" && optind + 2 < argc) {
            cmdType = CommandType::CALL_FUNCTION;
            targetPid = std::stoi(argv[optind + 1]);
            functionName = argv[optind + 2];
            
            // 如果有参数，解析JSON
            if (optind + 3 < argc) {
                params = json_tokener_parse(argv[optind + 3]);
                if (!params) {
                    std::cerr << "Invalid JSON parameters" << std::endl;
                    return false;
                }
            }
        } else {
            std::cerr << "Unknown command: " << command << std::endl;
            printHelp();
            return false;
        }
    } else {
        std::cerr << "No command specified" << std::endl;
        printHelp();
        return false;
    }

    return true;
}

void Config::printHelp() {
    std::cout << "Usage: testd [选项] [命令]\n\n";
    std::cout << "命令:\n";
    std::cout << "  run <测试用例文件>     运行指定测试用例\n";
    std::cout << "  server                启动控制服务器\n";
    std::cout << "  mock <进程ID> <函数>  直接Mock指定进程的函数\n";
    std::cout << "  call <进程ID> <函数>  直接调用指定进程的函数\n";
    std::cout << "\n";
    std::cout << "选项:\n";
    std::cout << "  -p, --port <端口>     指定TCP控制端口（默认8000）\n";
    std::cout << "  -v, --verbose         详细输出模式\n";
    std::cout << "  -c, --config <文件>   指定配置文件\n";
    std::cout << "  -o, --output <文件>   指定输出文件\n";
    std::cout << "  -h, --help            显示帮助信息\n";
    std::cout << "  -V, --version         显示版本信息\n";
}

void Config::printVersion() {
    std::cout << "testd version 1.0.0" << std::endl;
}

// ClientConnection类实现
ClientConnection::ClientConnection(int socketFd, pid_t pid, const std::string& programName)
    : socketFd(socketFd), pipeFd{0, 0, 0}, pid(pid), isChild(false), programName(programName), mode(TestMode::NORMAL), aspects(nullptr) {
}

ClientConnection::~ClientConnection() {
    if (aspects) {
        json_object_put(aspects);
    }
}

void ClientConnection::setAspects(struct json_object* a) {
    if (aspects) {
        json_object_put(aspects);
    }
    aspects = a;
    if (aspects) {
        json_object_get(aspects);
    }
}

void ClientConnection::updateClientData() {
    if (isChild) {
        // 从管道读取数据
        char buffer[1024];
        ssize_t n;
        while ((n = read(pipeFd[1], buffer, sizeof(buffer))) > 0) {
            stdoutBuffer.append(buffer, n);
        }
        while ((n = read(pipeFd[2], buffer, sizeof(buffer))) > 0) {
            stderrBuffer.append(buffer, n);
        }
    }
}

void ClientConnection::printClientData(std::ostream& os) {
    os << "============" << programName << "============" << std::endl;
    os << "PID: " << pid << std::endl;
    os << "------------stdout------------" << std::endl;
    os << stdoutBuffer << std::endl;
    os << "------------stderr------------" << std::endl;
    os << stderrBuffer << std::endl;
    os << "==============================" << std::endl;
}

// Context类实现
Context::Context() : running(false) {
}

Context& Context::getInstance() {
    static Context instance;
    return instance;
}

bool Context::init(int argc, char** argv) {
    CommandType cmdType = CommandType::NONE;
    std::string testFile;
    pid_t targetPid = 0;
    std::string functionName;
    struct json_object* params = nullptr;

    // 初始化消息系统
    MessageHandlerFactory::registerDefaultHandlers();

    // 解析命令行参数
    if (!config.parseArgs(argc, argv, cmdType, testFile, targetPid, functionName, params)) {
        return false;
    }
    
    // 根据命令类型执行相应操作
    int ret = 0;
    switch (cmdType) {
        case CommandType::SERVER:
            ret = runServer();
            break;
            
        case CommandType::RUN_TEST:
            ret = runTest(testFile);
            break;
            
        case CommandType::MOCK_FUNCTION:
            ret = mockFunction(targetPid, functionName, params);
            break;
            
        case CommandType::CALL_FUNCTION:
            ret = callFunction(targetPid, functionName, params);
            break;
            
        default:
            std::cerr << "Unknown command type" << std::endl;
            ret = -1;
            break;
    }
    
    // 清理参数
    if (params) {
        json_object_put(params);
    }
    
    return ret == 0;
}

void Context::cleanup() {
    // 关闭所有客户端连接
    clients.clear();
    
    // 关闭Socket
    unixSocket.reset();
    tcpSocket.reset();
    
    // 清理测试资源
    currentTest.reset();
    result.reset();
}

int Context::runServer() {
    // 初始化Unix Domain Socket
    //TODO    
    return 0;
}

int Context::runTest(const std::string& testFile) {
    // 加载测试用例
    currentTest = TestCase::loadFromFile(testFile);
    if (!currentTest) {
        std::cerr << "Failed to load test case: " << testFile << std::endl;
        return -1;
    }
    
    // 初始化Unix Domain Socket
    unixSocket = std::make_shared<UnixDomainSocket>();
    if (!unixSocket->init()) {
        std::cerr << "Failed to initialize Unix Domain Socket" << std::endl;
        return -1;
    }
        
    std::cout << "Test started: " << currentTest->getName() <<std::endl;
    running = true;
    
    // 主循环
    while (running && currentTest->getState() != Task::State::FINISHED && 
           currentTest->getState() != Task::State::ERROR) {
        // 处理测试用例事件
        int result = currentTest->execute();
        if (result < 0 && result != -EAGAIN) {
            std::cerr << "Test execution failed" << std::endl;
            break;
        }
        
        // 处理Socket和计时器事件
        fd_set read_fds;
        FD_ZERO(&read_fds);
        
        // 添加Unix Domain Socket
        int max_fd = unixSocket->getSocketFd();
        FD_SET(unixSocket->getSocketFd(), &read_fds);
        
        // 添加客户端Socket
        for (const auto& client : clients) {
            if (client->getSocketFd() > max_fd) {
                max_fd = client->getSocketFd();
            }
            FD_SET(client->getSocketFd(), &read_fds);
        }

        // 添加计时器fd
        if (currentTest->getWaitTimerFd() > 0) {
            if (currentTest->getWaitTimerFd() > max_fd) {
                max_fd = currentTest->getWaitTimerFd();
            }
            FD_SET(currentTest->getWaitTimerFd(), &read_fds);
        }
        
        // 设置超时
        struct timeval tv;
        tv.tv_sec = 0;
        tv.tv_usec = 100000;  // 100ms
        
        // 等待事件
        int ret = select(max_fd + 1, &read_fds, nullptr, nullptr, &tv);
        if (ret < 0) {
            if (errno == EINTR) {
                continue;
            }
            std::cerr << "select error: " << strerror(errno) << std::endl;
            break;
        }
        
        // 处理Unix Domain Socket连接
        if (FD_ISSET(unixSocket->getSocketFd(), &read_fds)) {
            auto client = unixSocket->acceptConnection();
            if (client) {
                clients.push_back(client);
                if (config.isVerbose()) {
                    std::cout << "New Unix client connected, fd=" << client->getSocketFd() << std::endl;
                }
            }
        }
        
        // 处理客户端数据
        for (auto it = clients.begin(); it != clients.end();) {
            if ((*it)->getSocketFd() == -1) {
                // 已经关闭的连接，直接移除
                (*it)->printClientData(std::cout);
                it = clients.erase(it);
                continue;
            }else if (FD_ISSET((*it)->getSocketFd(), &read_fds)) {
                struct json_object* jsonObj = nullptr;
                int ret;
                std::string data;
                ret = unixSocket->recvFromClient(*it, data);
                if (ret == -ECONNRESET) {
                    // 客户端关闭连接
                    if (config.isVerbose()) {
                        std::cout << "Client disconnected, fd=" << (*it)->getSocketFd() << std::endl;
                    }
                    (*it)->clearSocketFd();
                } else if (ret < 0) {
                    std::cerr << "Failed to receive JSON from client: " << -ret << std::endl;
                    (*it)->clearSocketFd();
                }
                do{
                    ret = unixSocket->parseJson(&jsonObj, data);
                    
                    if (ret < 0) {
                        break;
                    } else if (ret > 0) {
                        // 处理消息
                        JsonProtocol::processMessage(jsonObj, std::distance(clients.begin(), it));
                        json_object_put(jsonObj);
                    }
                }while(ret > 0);

            }
            (*it)->updateClientData();
            ++it;
        }

        // 处理消息系统中的待处理消息
        MessageSystem::getInstance().processMessages();
    }
    
    // 输出测试结果
    if (currentTest->getState() == Task::State::FINISHED) {
        std::cout << "Test finished: " << currentTest->getName() << std::endl;
        auto result = currentTest->getResult();
        std::cout << "Total cases: " << result->getTotal() << std::endl;
        std::cout << "Passed cases: " << result->getPassed() << std::endl;
        std::cout << "Failed cases: " << result->getFailed() << std::endl;
    } else {
        std::cout << "Test failed: " << currentTest->getName() << std::endl;
    }
    
    return (currentTest->getState() == Task::State::FINISHED) ? 0 : -1;
}

int Context::mockFunction(pid_t pid, const std::string& funcName, struct json_object* params) {
    //TODO
    (void)params;
    (void)pid;
    (void)funcName;
    return 0;
}

int Context::callFunction(pid_t pid, const std::string& funcName, struct json_object* params) {
    // 初始化Unix Domain Socket
    unixSocket = std::make_shared<UnixDomainSocket>();
    if (!unixSocket->init()) {
        std::cerr << "Failed to initialize Unix Domain Socket" << std::endl;
        return -1;
    }
    
    // 等待客户端连接
    std::cout << "Waiting for client connection..." << std::endl;
    running = true;
    
    while (running && clients.empty()) {
        fd_set read_fds;
        FD_ZERO(&read_fds);
        
        // 添加Unix Domain Socket
        FD_SET(unixSocket->getSocketFd(), &read_fds);
        
        // 设置超时
        struct timeval tv;
        tv.tv_sec = 1;
        tv.tv_usec = 0;
        
        // 等待事件
        int ret = select(unixSocket->getSocketFd() + 1, &read_fds, nullptr, nullptr, &tv);
        if (ret < 0) {
            if (errno == EINTR) {
                continue;
            }
            std::cerr << "select error: " << strerror(errno) << std::endl;
            return -1;
        }
        
        // 处理Unix Domain Socket连接
        if (FD_ISSET(unixSocket->getSocketFd(), &read_fds)) {
            auto client = unixSocket->acceptConnection();
            if (client) {
                clients.push_back(client);
                if (config.isVerbose()) {
                    std::cout << "New Unix client connected, fd=" << client->getSocketFd() << std::endl;
                }
            }
        }
    }
    
    if (clients.empty()) {
        std::cerr << "No client connected" << std::endl;
        return -1;
    }
    
    // 查找目标进程
    auto client = unixSocket->findClientByPid(pid);
    if (!client) {
        std::cerr << "Process not found: " << pid << std::endl;
        return -1;
    }
    
    // 创建调用请求
    struct json_object* callRequest = JsonProtocol::createCallRequest(funcName, params);
    if (!callRequest) {
        std::cerr << "Failed to create call request" << std::endl;
        return -1;
    }
    
    // 发送请求
    int ret = unixSocket->sendJson(client, callRequest);
    json_object_put(callRequest);
    
    if (ret < 0) {
        std::cerr << "Failed to send call request: " << -ret << std::endl;
        return -1;
    }
    
    std::cout << "Call function: pid=" << pid << ", function=" << funcName << std::endl;
    return 0;
}

std::shared_ptr<ClientConnection> Context::findClientByProgramName(const std::string& programName) {
    for (const auto& client : clients) {
        if (client->getProgramName() == programName) {
            return client;
        }
    }
    return nullptr;
}



} // namespace testd
